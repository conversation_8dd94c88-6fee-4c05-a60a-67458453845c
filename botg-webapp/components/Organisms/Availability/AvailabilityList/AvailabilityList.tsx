'use client';

import Stack from '@mui/material/Stack';
import Typography from '@/components/Atoms/Typography';
import { customPalettes } from '@/theme/customPalettes';
import {
  StyledAvailableRoomItem,
  StyledContainer,
  StyledCounterText,
  StyledEmployeeText,
  StyledListBody,
  StyledListHeader,
  StyledEmployeeCounterText,
} from './AvailabilityList.styled';
import { TAvailabilityListProps } from './AvailabilityList.types';

export const AvailabilityList: React.FC<TAvailabilityListProps> = ({ data }) => {
  const allEmployeeCount = data?.availableEmployees?.length || 0;

  return (
    <StyledContainer>
      <StyledListHeader>
        <Typography
          variant="heading-medium-700"
          color={customPalettes?.primary?.main}
          textTransform="capitalize"
          flex="55%"
        >
          NUMBER OF AVAILABLE ROOMS
        </Typography>
        <Stack flexDirection="row" flex="45%" justifyContent="space-between" alignItems="center">
          <Typography variant="heading-medium-700" color={customPalettes?.primary?.main} textTransform="capitalize">
            LIST OF AVAILABLE THERAPISTS
          </Typography>
          <StyledEmployeeCounterText>{allEmployeeCount}</StyledEmployeeCounterText>
        </Stack>
      </StyledListHeader>
      <StyledListBody>
        <Stack flexDirection="row" flex="55%" alignItems="center" gap={1} flexWrap="wrap">
          {data?.availableRooms?.map(room => (
            <StyledAvailableRoomItem key={room?.id}>
              <Typography variant="body-xlarge-400" color={customPalettes?.neutrals?.N50?.main}>
                {room?.name || ''}
              </Typography>
              <StyledCounterText $isavailable={!!room?.roomQuantity}> {room?.roomQuantity || 0}</StyledCounterText>
            </StyledAvailableRoomItem>
          ))}
        </Stack>
        <Stack flexDirection="row" flex="45%" alignItems="center" gap={1} flexWrap="wrap">
          {data?.availableEmployees?.map(employee => (
            <StyledEmployeeText key={employee?.id}>{employee?.name || ''}</StyledEmployeeText>
          ))}
        </Stack>
      </StyledListBody>
    </StyledContainer>
  );
};
