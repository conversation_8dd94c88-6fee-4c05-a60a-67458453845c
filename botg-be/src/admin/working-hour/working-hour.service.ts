import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  Brackets,
  Equal,
  FindOperator,
  ILike,
  In,
  IsNull,
  LessThanOrEqual,
  Like,
  MoreThan,
  MoreThanOrEqual,
  Repository,
} from 'typeorm';
import { BaseCrudService } from 'src/core/base/base-crud.service';
import { WorkingHour } from './working-hour.entity';
import { CreateWorkHourDto } from './dto/createWorkingHour.dto';
import { VirtualWorkingHour } from './virtual-working-hour.entity';
import { Employee } from '../employee/employee.entity';
import { WHTimework } from './wh-time-work.entity';
import { WHTimer } from './wh-timer.entity';
import moment = require('moment');
import {
  WorkingHourDateResponse,
  WorkingHourResponse,
} from './dto/listWorkingHour.dto';
import {
  clampMaxDate,
  clampMinDate,
  clearDateTime,
  isDateEqual,
  timerToString,
} from 'src/core/base/utils';
import { DeleteWorkingHourQueryDto } from './dto/deleteWorkingHour.dto';
import { UUID } from 'crypto';
import { HOUR, MINUTE } from 'src/core/common/common.types';
import { isUUID } from 'class-validator';
import { DayOffService } from '../day-off/day-off.service';
import { DayOff } from '../day-off/day-off.entity';

@Injectable()
export class WorkingHourService extends BaseCrudService<WorkingHour> {
  constructor(
    @InjectRepository(WorkingHour) repo: Repository<WorkingHour>,
    @InjectRepository(VirtualWorkingHour)
    private virtualWorkingHourRepo: Repository<VirtualWorkingHour>,
    @InjectRepository(Employee)
    private employeeRepo: Repository<Employee>,
    @InjectRepository(WHTimer)
    private whTimerRepo: Repository<WHTimer>, // @InjectRepository(WHTimework) // private whTimeworkRepo: Repository<WHTimework>,
    @InjectRepository(DayOff)
    private dayOffRepo: Repository<DayOff>,
  ) {
    super(repo);
  }

  private static copyShiftTimes(shifts: WHTimework[]) {
    return shifts.map(
      (shift) =>
        new WHTimework({
          startTime: new WHTimer({
            hour: shift.startTime.hour,
            minute: shift.startTime.minute,
          }),
          endTime: new WHTimer({
            hour: shift.endTime.hour,
            minute: shift.endTime.minute,
          }),
        }),
    );
  }

  async setWorkingHour(
    employeeId: string,
    { date, shiftTimes, repeat }: CreateWorkHourDto,
  ) {
    const employee = await this.employeeRepo.findOne({
      where: { id: employeeId },
      relations: ['branch'],
    });
    if (!employee) {
      throw new BadRequestException();
    }

    const branch = employee.branch;
    const clearedDate = clearDateTime(date);
    const dayOfWeek = clearedDate.getDay();

    let existDate = await this.findOne({
      where: {
        date: Equal(clearedDate),
        dayOfWeek,
        employee: { id: employeeId },
        branch: { id: branch.id },
      },
      relations: ['virtualRepeat', 'employee', 'branch'],
    });
    const virtualRepeat = await this.virtualWorkingHourRepo.findOne({
      where: [
        {
          dayOfWeek,
          employee: { id: employeeId },
          branch: { id: branch.id },
          startDate: LessThanOrEqual(clearedDate),
          endDate: MoreThanOrEqual(clearedDate),
        },
        {
          dayOfWeek,
          employee: { id: employeeId },
          branch: { id: branch.id },
          startDate: LessThanOrEqual(clearedDate),
          endDate: IsNull(),
        },
      ],
      relations: [
        'workingHour',
        'shiftTimes',
        'shiftTimes.startTime',
        'shiftTimes.endTime',
        'workingHour.employee',
        'workingHour.branch',
      ],
    });

    function makeShiftTimes() {
      return shiftTimes.map(
        (shift) =>
          new WHTimework({
            startTime: new WHTimer({
              hour: shift.startTime.hour,
              minute: shift.startTime.minute,
            }),
            endTime: new WHTimer({
              hour: shift.endTime.hour,
              minute: shift.endTime.minute,
            }),
          }),
      );
    }

    await this.repo.manager.transaction(async (trans) => {
      if (!existDate) {
        existDate = await trans.save(WorkingHour, {
          date: clearedDate,
          dayOfWeek,
          employee,
          branch,
          shiftTimes: makeShiftTimes(),
        });
      } else {
        await trans.save(WorkingHour, {
          ...existDate,
          // only update shift times not update employee and branch
          shiftTimes: makeShiftTimes(),
        });
      }

      // is inside virtual range
      if (virtualRepeat) {
        if (virtualRepeat.workingHour.id !== existDate.id) {
          const previousWeek = moment(clearedDate).subtract(7, 'days');

          const diff = moment(previousWeek).diff(
            virtualRepeat.workingHour.date,
            'days',
          );
          if (diff === 0) {
            await trans.delete(VirtualWorkingHour, { id: virtualRepeat.id });
          } else if (diff > 0) {
            // breaking virtual range into [virtualRepeat.start -> previousWeek]
            await trans.delete(VirtualWorkingHour, { id: virtualRepeat.id });
            await trans.save(WorkingHour, {
              ...virtualRepeat.workingHour,
              shiftTimes: WorkingHourService.copyShiftTimes(
                virtualRepeat.shiftTimes,
              ),
              virtualRepeat: {
                dayOfWeek,
                employee: virtualRepeat.workingHour.employee,
                branch: virtualRepeat.workingHour.branch,
                // breaking keep old shift times
                shiftTimes: WorkingHourService.copyShiftTimes(
                  virtualRepeat.shiftTimes,
                ),
                startDate: virtualRepeat.workingHour.date,
                endDate: previousWeek.toDate(), // repeat to previous week of request date
              } as VirtualWorkingHour,
            });
          }
          if (repeat) {
            // update new virtual range
            await trans.delete(VirtualWorkingHour, { id: virtualRepeat.id });
            await trans.save(WorkingHour, {
              ...existDate,
              virtualRepeat: {
                dayOfWeek,
                employee: existDate.employee,
                branch: existDate.branch,
                shiftTimes: makeShiftTimes(),
                startDate: clearedDate,
                endDate: null, // repeat to infinity
              } as VirtualWorkingHour,
            });
          } else {
            // breaking virtual range into [nextWeek -> virtualRepeat.endDate]
            const nextWeek = moment(clearedDate).add(7, 'days').toDate();

            const virtualStartDate = clampMaxDate(
              nextWeek,
              virtualRepeat.endDate,
            );
            const virtualEndDate = virtualRepeat.endDate;

            let newWorkingHour: WorkingHour;
            if (!isDateEqual(virtualStartDate, clearedDate)) {
              newWorkingHour = await trans.save(WorkingHour, {
                date: virtualStartDate,
                dayOfWeek,
                employee,
                branch,
                // breaking keep old shift times
                shiftTimes: WorkingHourService.copyShiftTimes(
                  virtualRepeat.shiftTimes,
                ),
              });
            }

            if (isDateEqual(virtualStartDate, virtualEndDate)) {
              await trans.delete(VirtualWorkingHour, {
                id: virtualRepeat.id,
              });
            } else {
              if (newWorkingHour) {
                await trans.save(WorkingHour, {
                  ...newWorkingHour,
                  virtualRepeat: {
                    dayOfWeek,
                    employee: virtualRepeat.workingHour.employee,
                    branch: virtualRepeat.workingHour.branch,
                    shiftTimes: WorkingHourService.copyShiftTimes(
                      virtualRepeat.shiftTimes,
                    ),
                    startDate: virtualStartDate,
                    endDate: virtualEndDate,
                  } as VirtualWorkingHour,
                });
              }
            }
          }
        } else {
          await trans.delete(VirtualWorkingHour, { id: virtualRepeat.id });
          if (repeat) {
            // update new virtual range
            const newVirtualWorkingHour = await trans.save(VirtualWorkingHour, {
              dayOfWeek,
              employee: existDate.employee,
              branch: existDate.branch,
              shiftTimes: makeShiftTimes(),
              startDate: clearedDate,
              endDate: null, // repeat to infinity
              workingHour: existDate,
            });

            await trans.save(WorkingHour, {
              ...existDate,
              virtualRepeat: newVirtualWorkingHour,
            });
          }
        }
      } else {
        if (repeat) {
          // clear other working hour
          await trans.delete(WorkingHour, {
            date: MoreThan(clearedDate),
            dayOfWeek,
            employee,
            branch,
          });

          // set new virtual range
          const newVirtualWorkingHour = await trans.save(VirtualWorkingHour, {
            dayOfWeek,
            employee: existDate.employee,
            branch: existDate.branch,
            shiftTimes: makeShiftTimes(),
            startDate: clearedDate,
            endDate: null, // repeat to infinity
            workingHour: existDate,
          });

          await trans.save(WorkingHour, {
            ...existDate,
            virtualRepeat: newVirtualWorkingHour,
          });
        }
      }
    });
  }

  async setWorkingHourV1(
    employeeId: string,
    { date, shiftTimes, repeat }: CreateWorkHourDto,
  ) {
    function makeShiftTimes() {
      return shiftTimes.map(
        (shift) =>
          new WHTimework({
            startTime: new WHTimer({
              hour: shift.startTime.hour,
              minute: shift.startTime.minute,
            }),
            endTime: new WHTimer({
              hour: shift.endTime.hour,
              minute: shift.endTime.minute,
            }),
          }),
      );
    }

    const employee = await this.employeeRepo.findOne({
      where: { id: employeeId },
      relations: ['branch'],
    });
    if (!employee) {
      throw new BadRequestException();
    }

    const branch = employee.branch;
    const clearedDate = clearDateTime(date);
    const dayOfWeek = clearedDate.getDay();
    try {
      await this.repo.manager.transaction(async (trans) => {
        if (repeat) {
          // 1. rm all working_hour is dayOfWeek and >= clearedDate
          await trans.delete(WorkingHour, {
            date: MoreThanOrEqual(clearedDate),
            dayOfWeek,
            employee: { id: employeeId },
            branch: { id: branch.id },
          });
          // 2. update or create virtual
          const virtualRepeat = await this.virtualWorkingHourRepo.findOne({
            where: [
              {
                dayOfWeek,
                employee: { id: employeeId },
                branch: { id: branch.id },
                startDate: LessThanOrEqual(clearedDate),
                endDate: MoreThanOrEqual(clearedDate),
              },
              {
                dayOfWeek,
                employee: { id: employeeId },
                branch: { id: branch.id },
                startDate: LessThanOrEqual(clearedDate),
                endDate: IsNull(),
              },
            ],
            relations: [
              'workingHour',
              'shiftTimes',
              'shiftTimes.startTime',
              'shiftTimes.endTime',
              'workingHour.employee',
              'workingHour.branch',
            ],
          });

          // 2.1 check exist virtual-hour
          if (virtualRepeat) {
            const previousWeek = moment(clearedDate)
              .subtract(7, 'days')
              .toDate();
            const startDate = virtualRepeat.startDate;
            const endDate = clampMinDate(previousWeek, virtualRepeat.startDate);

            //2.1.1 check startDate is equal to virtual-hour's startDate
            if (!isDateEqual(clearedDate, virtualRepeat.startDate)) {
              //create new virtual
              await trans.save(VirtualWorkingHour, {
                startDate,
                endDate,
                dayOfWeek,
                employee,
                branch,
                shiftTimes: WorkingHourService.copyShiftTimes(
                  virtualRepeat.shiftTimes,
                ),
              });
            }
            await trans.delete(VirtualWorkingHour, { id: virtualRepeat.id });
            await trans.delete(VirtualWorkingHour, {
              startDate: MoreThan(clearedDate),
              dayOfWeek,
              employee: { id: employeeId },
              branch: { id: branch.id },
            });
            await trans.delete(WorkingHour, {
              date: MoreThanOrEqual(clearedDate),
              dayOfWeek,
              employee: { id: employeeId },
              branch: { id: branch.id },
            });

            // Create new working hour record for the current date
            const workingHour = await trans.save(WorkingHour, {
              date: clearedDate,
              dayOfWeek,
              employee,
              branch,
              shiftTimes: makeShiftTimes(),
            });

            // Create new virtual working hour that will apply to future dates
            await trans.save(VirtualWorkingHour, {
              dayOfWeek,
              employee,
              branch,
              shiftTimes: makeShiftTimes(),
              startDate: clearedDate,
              endDate: null, // repeat to infinity
              workingHour, // Link the virtual record to the working hour
            });
          }
          // 2.2
          else {
            // Create new working hour record for the current date
            const workingHour = await trans.save(WorkingHour, {
              date: clearedDate,
              dayOfWeek,
              employee,
              branch,
              shiftTimes: makeShiftTimes(),
            });

            //create new virtual
            await trans.save(VirtualWorkingHour, {
              dayOfWeek,
              employee,
              branch,
              shiftTimes: makeShiftTimes(),
              startDate: clearedDate,
              endDate: null, // repeat to infinity
              workingHour, // Link the virtual record to the working hour
            });
          }
        } else {
          //1. find working_hour
          let existDate = await this.findOne({
            where: {
              date: Equal(clearedDate),
              dayOfWeek,
              employee: { id: employeeId },
              branch: { id: branch.id },
            },
            relations: [
              'virtualRepeat',
              'employee',
              'branch',
              'shiftTimes',
              'shiftTimes.startTime',
              'shiftTimes.endTime',
            ],
          });

          //2. if exists => update working_hour
          //3. if not exists => create working_hour
          if (!existDate) {
            existDate = await trans.save(WorkingHour, {
              date: clearedDate,
              dayOfWeek,
              employee,
              branch,
              shiftTimes: makeShiftTimes(),
            });
          } else {
            // First, delete all existing shift times and their related timer entities
            if (existDate.shiftTimes && existDate.shiftTimes.length > 0) {
              // Delete timers first (both start and end times)
              for (const shiftTime of existDate.shiftTimes) {
                if (shiftTime.startTime) {
                  await trans.delete(WHTimer, { id: shiftTime.startTime.id });
                }
                if (shiftTime.endTime) {
                  await trans.delete(WHTimer, { id: shiftTime.endTime.id });
                }
                // Then delete the shift time itself
                await trans.delete(WHTimework, { id: shiftTime.id });
              }
            }

            // Now create a new object with only the fields we want to update
            // This avoids any confusion with the ORM cached objects
            const updatedWorkingHour = {
              id: existDate.id,
              shiftTimes: makeShiftTimes(),
            };

            // Save the updated entity with new shift times
            await trans.save(WorkingHour, updatedWorkingHour);
          }
        }
      });
      return true;
    } catch (e) {
      console.error('Error updating working hours:', e);
      throw new BadRequestException('Failed to update working hours');
    }
  }

  async deleteWorkingHourV1(
    employeeId: string,
    { date, repeat = false }: DeleteWorkingHourQueryDto,
  ) {
    const employee = await this.employeeRepo.findOne({
      where: { id: employeeId },
      relations: ['branch'],
    });
    if (!employee) {
      throw new BadRequestException();
    }
    const branch = employee.branch;
    const clearedDate = clearDateTime(date);
    const dayOfWeek = clearedDate.getDay();

    const existDate = await this.findOne({
      where: {
        date: Equal(clearedDate),
        dayOfWeek,
        employee: { id: employeeId },
        branch: { id: branch.id },
      },
      relations: ['virtualRepeat', 'employee', 'branch'],
    });
    const virtualRepeat = await this.virtualWorkingHourRepo.findOne({
      where: [
        {
          dayOfWeek,
          employee: { id: employeeId },
          branch: { id: branch.id },
          startDate: LessThanOrEqual(clearedDate),
          endDate: MoreThanOrEqual(clearedDate),
        },
        {
          dayOfWeek,
          employee: { id: employeeId },
          branch: { id: branch.id },
          startDate: LessThanOrEqual(clearedDate),
          endDate: IsNull(),
        },
      ],
      relations: [
        'workingHour',
        'shiftTimes',
        'shiftTimes.startTime',
        'shiftTimes.endTime',
        'workingHour.employee',
        'workingHour.branch',
      ],
    });

    await this.repo.manager.transaction(async (trans) => {
      // [A, B, C , D, E] remove C  => [A, B] vs [D ,E]
      // [A, B, C , D] remove C  => [A, B] vs [D, D] => [A, B] vs D
      // [A, B, C ] remove C  => [A, B] vs [C, C] => [A, B]

      // [A, B, C , D] remove B  => [A, A] vs [C ,D] => A vs [C, D]
      // [A, B, C ] remove B  => [A, A] vs [C , C] => A vs C
      // [A, B ] remove B  => [A, A] vs [B, B] => A

      // [A, B, C] remove A  => [A, A] vs [B ,C] => [B ,C]
      // [A, B ] remove A  => [A, A] vs [B, B] => B
      if (virtualRepeat) {
        {
          const previousWeek = moment(clearedDate).subtract(7, 'days').toDate();
          const startDate = virtualRepeat.startDate;
          const endDate = clampMinDate(previousWeek, virtualRepeat.startDate);

          if (
            // only create new working hour when not exist
            !isDateEqual(startDate, virtualRepeat.startDate)
          ) {
            await trans.save(WorkingHour, {
              date: startDate,
              dayOfWeek,
              employee,
              branch,
              shiftTimes: WorkingHourService.copyShiftTimes(
                virtualRepeat.shiftTimes,
              ),
              virtualRepeat: {
                startDate,
                endDate,
                dayOfWeek,
                employee,
                branch,
                shiftTimes: WorkingHourService.copyShiftTimes(
                  virtualRepeat.shiftTimes,
                ),
              },
            });
          } else {
            // update virtual range
            await trans.delete(VirtualWorkingHour, { id: virtualRepeat.id });
            await trans.save(WorkingHour, {
              ...virtualRepeat.workingHour,
              virtualRepeat: isDateEqual(startDate, endDate)
                ? null
                : {
                    startDate,
                    endDate,
                    dayOfWeek,
                    employee,
                    branch,
                    shiftTimes: WorkingHourService.copyShiftTimes(
                      virtualRepeat.shiftTimes,
                    ),
                  },
            });
          }
        }

        if (existDate) {
          await trans.delete(WorkingHour, { id: existDate.id });
        }

        // if delete repeat mean not generate continuous shifts
        const keepContinuousShifts = !repeat;
        if (keepContinuousShifts) {
          const nextWeek = moment(clearedDate).add(7, 'days').toDate();
          const startDate = clampMaxDate(nextWeek, virtualRepeat.endDate);
          const endDate = virtualRepeat.endDate;

          // is not delete date
          if (!isDateEqual(startDate, clearedDate)) {
            await trans.save(WorkingHour, {
              date: startDate,
              dayOfWeek,
              employee,
              branch,
              shiftTimes: WorkingHourService.copyShiftTimes(
                virtualRepeat.shiftTimes,
              ),
              virtualRepeat: isDateEqual(startDate, endDate)
                ? null
                : {
                    startDate,
                    endDate,
                    dayOfWeek,
                    employee,
                    branch,
                    shiftTimes: WorkingHourService.copyShiftTimes(
                      virtualRepeat.shiftTimes,
                    ),
                  },
            });
          }
        }
      } else {
        if (existDate) {
          await trans.delete(WorkingHour, {
            id: existDate.id,
          });
        }
      }
      // clear all date from clearedDate to infinity
      if (repeat) {
        await trans.delete(WorkingHour, {
          date: MoreThan(clearedDate),
          dayOfWeek,
          employee: { id: employeeId },
          branch: { id: branch.id },
        });
      }
    });
  }

  // ===== HELPER METHODS FOR V2 IMPLEMENTATION =====

  /**
   * Create shift times from DTO
   */
  private createShiftTimes(shiftTimes: any[]): WHTimework[] {
    return shiftTimes.map(
      (shift) =>
        new WHTimework({
          startTime: new WHTimer({
            hour: shift.startTime.hour,
            minute: shift.startTime.minute,
          }),
          endTime: new WHTimer({
            hour: shift.endTime.hour,
            minute: shift.endTime.minute,
          }),
        }),
    );
  }

  /**
   * Find existing working hour for a specific date
   */
  private async findExistingWorkingHour(
    employeeId: string,
    branchId: string,
    date: Date,
  ): Promise<WorkingHour | null> {
    const dayOfWeek = date.getDay();
    return this.findOne({
      where: {
        date: Equal(date),
        dayOfWeek,
        employee: { id: employeeId },
        branch: { id: branchId },
      },
      relations: ['virtualRepeat', 'employee', 'branch', 'shiftTimes'],
    });
  }

  /**
   * Find virtual working hour that covers a specific date
   */
  private async findVirtualWorkingHour(
    employeeId: string,
    branchId: string,
    date: Date,
  ): Promise<VirtualWorkingHour | null> {
    const dayOfWeek = date.getDay();
    return this.virtualWorkingHourRepo.findOne({
      where: [
        {
          dayOfWeek,
          employee: { id: employeeId },
          branch: { id: branchId },
          startDate: LessThanOrEqual(date),
          endDate: MoreThanOrEqual(date),
        },
        {
          dayOfWeek,
          employee: { id: employeeId },
          branch: { id: branchId },
          startDate: LessThanOrEqual(date),
          endDate: IsNull(),
        },
      ],
      relations: [
        'workingHour',
        'shiftTimes',
        'shiftTimes.startTime',
        'shiftTimes.endTime',
        'workingHour.employee',
        'workingHour.branch',
      ],
    });
  }

  /**
   * Create or update working hour for a specific date
   */
  private async createOrUpdateWorkingHour(
    trans: any,
    employee: any,
    branch: any,
    date: Date,
    shiftTimes: WHTimework[],
    existingWorkingHour?: WorkingHour,
  ): Promise<WorkingHour> {
    const dayOfWeek = date.getDay();

    if (existingWorkingHour) {
      return trans.save(WorkingHour, {
        ...existingWorkingHour,
        shiftTimes,
      });
    } else {
      return trans.save(WorkingHour, {
        date,
        dayOfWeek,
        employee,
        branch,
        shiftTimes,
      });
    }
  }

  /**
   * Handle virtual working hour creation/update for repeat functionality
   */
  private async handleVirtualWorkingHour(
    trans: any,
    employee: any,
    branch: any,
    date: Date,
    shiftTimes: WHTimework[],
    repeat: boolean,
    workingHour: WorkingHour,
    existingVirtual?: VirtualWorkingHour,
  ): Promise<void> {
    const dayOfWeek = date.getDay();

    if (repeat) {
      // If repeat is enabled, create/update virtual working hour
      if (existingVirtual) {
        // Update existing virtual working hour
        await trans.delete(VirtualWorkingHour, { id: existingVirtual.id });
      }

      // Clear future working hours for this day of week
      await trans.delete(WorkingHour, {
        date: MoreThan(date),
        dayOfWeek,
        employee,
        branch,
      });

      // Create new virtual working hour
      const newVirtualWorkingHour = await trans.save(VirtualWorkingHour, {
        dayOfWeek,
        employee,
        branch,
        shiftTimes: WorkingHourService.copyShiftTimes(shiftTimes),
        startDate: date,
        endDate: null, // repeat to infinity
        workingHour,
      });

      // Link working hour to virtual
      await trans.save(WorkingHour, {
        ...workingHour,
        virtualRepeat: newVirtualWorkingHour,
      });
    } else if (existingVirtual) {
      // If repeat is disabled but virtual exists, handle breaking the pattern
      await this.breakVirtualPattern(
        trans,
        employee,
        branch,
        date,
        shiftTimes,
        existingVirtual,
      );
    }
  }

  /**
   * Break virtual working hour pattern when repeat is disabled
   */
  private async breakVirtualPattern(
    trans: any,
    employee: any,
    branch: any,
    date: Date,
    shiftTimes: WHTimework[],
    virtualRepeat: VirtualWorkingHour,
  ): Promise<void> {
    const dayOfWeek = date.getDay();

    // Create virtual range for dates before current date
    const previousWeek = moment(date).subtract(7, 'days').toDate();
    const startDate = virtualRepeat.startDate;
    const endDate = moment
      .min(moment(previousWeek), moment(virtualRepeat.startDate))
      .toDate();

    if (!moment(startDate).isSame(moment(virtualRepeat.startDate))) {
      await trans.save(VirtualWorkingHour, {
        startDate,
        endDate,
        dayOfWeek,
        employee,
        branch,
        shiftTimes: WorkingHourService.copyShiftTimes(virtualRepeat.shiftTimes),
      });
    }

    // Create virtual range for dates after current date (if needed)
    const nextWeek = moment(date).add(7, 'days').toDate();
    const futureStartDate = moment
      .max(moment(nextWeek), moment(virtualRepeat.endDate || nextWeek))
      .toDate();
    const futureEndDate = virtualRepeat.endDate;

    if (futureEndDate && !moment(futureStartDate).isSame(moment(date))) {
      await trans.save(VirtualWorkingHour, {
        startDate: futureStartDate,
        endDate: futureEndDate,
        dayOfWeek,
        employee,
        branch,
        shiftTimes: WorkingHourService.copyShiftTimes(virtualRepeat.shiftTimes),
      });
    }

    // Delete original virtual working hour
    await trans.delete(VirtualWorkingHour, { id: virtualRepeat.id });
  }

  /**
   * V2 Implementation: Simplified working hour setting with better error handling
   */
  async setWorkingHourV2(
    employeeId: string,
    { date, shiftTimes, repeat }: CreateWorkHourDto,
  ): Promise<void> {
    try {
      // 1. Validate employee
      const employee = await this.employeeRepo.findOne({
        where: { id: employeeId },
        relations: ['branch'],
      });
      if (!employee) {
        throw new BadRequestException('Employee not found');
      }

      const branch = employee.branch;
      const clearedDate = clearDateTime(date);

      // 2. Find existing data
      const [existingWorkingHour, existingVirtual] = await Promise.all([
        this.findExistingWorkingHour(employeeId, branch.id, clearedDate),
        this.findVirtualWorkingHour(employeeId, branch.id, clearedDate),
      ]);

      // 3. Create shift times
      const newShiftTimes = this.createShiftTimes(shiftTimes);

      // 4. Execute in transaction
      await this.repo.manager.transaction(async (trans) => {
        // Create or update working hour
        const workingHour = await this.createOrUpdateWorkingHour(
          trans,
          employee,
          branch,
          clearedDate,
          newShiftTimes,
          existingWorkingHour,
        );

        // Handle virtual working hour (repeat functionality)
        await this.handleVirtualWorkingHour(
          trans,
          employee,
          branch,
          clearedDate,
          newShiftTimes,
          repeat,
          workingHour,
          existingVirtual,
        );
      });
    } catch (error) {
      throw new BadRequestException(
        `Failed to set working hour: ${error.message}`,
      );
    }
  }

  async deleteWorkingHour(
    employeeId: string,
    { date, startTime, endTime, repeat = false }: DeleteWorkingHourQueryDto,
  ) {
    // try {
    const employee = await this.employeeRepo.findOne({
      where: { id: employeeId },
      relations: ['branch'],
    });
    if (!employee) {
      throw new BadRequestException();
    }
    const branch = employee.branch;
    const clearedDate = clearDateTime(date);
    const dayOfWeek = clearedDate.getDay();

    const existDate = await this.findOne({
      where: {
        date: Equal(clearedDate),
        dayOfWeek,
        employee: { id: employeeId },
        branch: { id: branch.id },
      },
      relations: [
        'virtualRepeat',
        'employee',
        'branch',
        'shiftTimes',
        'shiftTimes.startTime',
        'shiftTimes.endTime',
      ],
    });

    const virtualRepeat = await this.virtualWorkingHourRepo.findOne({
      where: [
        {
          dayOfWeek,
          employee: { id: employeeId },
          startDate: LessThanOrEqual(clearedDate),
          endDate: MoreThanOrEqual(clearedDate),
        },
        {
          dayOfWeek,
          employee: { id: employeeId },
          startDate: LessThanOrEqual(clearedDate),
          endDate: IsNull(),
        },
      ],
      relations: [
        'workingHour',
        'shiftTimes',
        'shiftTimes.startTime',
        'shiftTimes.endTime',
        'workingHour.employee',
        'workingHour.branch',
      ],
    });
    const [hourStart, minuteStart] = startTime.split(':');
    const [hourEnd, minuteEnd] = endTime.split(':');

    // const aaa = await this.whTimerRepo.find({
    //   where: [
    //     {
    //       refStart: {
    //         workingHour: {
    //           date: MoreThan(clearedDate),
    //           dayOfWeek,
    //           employee: { id: employeeId },
    //         },
    //       },
    //       hour: +hourStart as HOUR,
    //       minute: +minuteStart as MINUTE,
    //     },
    //     {
    //       refStart: {
    //         workingHour: {
    //           date: MoreThan(clearedDate),
    //           dayOfWeek,
    //           employee: { id: employeeId },
    //         },
    //       },
    //       hour: +hourEnd as HOUR,
    //       minute: +minuteEnd as MINUTE,
    //     },
    //   ],
    //   relations: [
    //     'refStart',
    //     'refEnd',
    //     'refStart.workingHour',
    //     'refEnd.workingHour',
    //   ],
    // });

    //repeat === true
    // del all whtimer of dateOfWeek with date > request date
    // create whtimer of vituralTime

    //repeat === false
    // existDate !== null => del whtimer of existDate
    // existDate === null => create whtimer of existDate

    const newShiftTimes = WorkingHourService.copyShiftTimes(
      virtualRepeat.shiftTimes,
    ).filter(
      (obj) =>
        !moment(
          [obj.startTime.hour, obj.startTime.minute].join(':'),
          'HH:mm',
        ).isSame(moment(startTime, 'HH:mm')) ||
        !moment(
          [obj.endTime.hour, obj.endTime.minute].join(':'),
          'HH:mm',
        ).isSame(moment(endTime, 'HH:mm')),
    );

    if (repeat) {
      await this.repo.manager.transaction(async (trans) => {
        if (virtualRepeat) {
          if (!isDateEqual(clearedDate, virtualRepeat.startDate)) {
            await trans.update(
              VirtualWorkingHour,
              { id: virtualRepeat.id },
              {
                endDate: moment(clearedDate).subtract(7, 'days').toDate(),
              },
            );
            if (newShiftTimes.length > 0) {
              await trans.save(VirtualWorkingHour, {
                // startDate: moment(clearedDate).add(7, 'days').toDate(),
                startDate: moment(clearedDate).toDate(),
                dayOfWeek,
                employee,
                branch,
                shiftTimes: newShiftTimes,
              });
            }
          } else {
            if (newShiftTimes.length > 0) {
              await trans.save(VirtualWorkingHour, {
                id: virtualRepeat.id,
                dayOfWeek,
                employee,
                branch,
                shiftTimes: newShiftTimes,
              });
            } else {
              await trans.delete(VirtualWorkingHour, {
                id: virtualRepeat.id,
              });
            }
            const WHTimeWork = await trans.find(WHTimework, {
              where: [
                { virtualWorkingHour: { id: virtualRepeat.id } },
                {
                  workingHour: { id: existDate?.id },
                },
              ],
            });
            if (existDate?.shiftTimes.length <= 1) {
              trans.delete(WorkingHour, {
                id: existDate.id,
              });
            }
            const WHTimeWorkByStart = await trans.find(WHTimer, {
              where: {
                refStart: {
                  id: In(WHTimeWork.map((t) => t.id)),
                },
                hour: hourStart as any,
                minute: minuteStart as any,
              },
              relations: ['refStart'],
            });

            const WHTimeWorkByEnd = await trans.find(WHTimer, {
              where: {
                refEnd: {
                  id: In(WHTimeWork.map((t) => t.id)),
                },
                hour: hourStart as any,
                minute: minuteStart as any,
              },
              relations: ['refEnd'],
            });
            for (const i of WHTimeWorkByStart) {
              await trans.delete(WHTimer, {
                id: i.id,
              });
              await trans.delete(WHTimework, {
                id: i.refStart?.id,
              });
            }

            for (const i of WHTimeWorkByEnd) {
              await trans.delete(WHTimer, {
                id: i.id,
              });
              await trans.delete(WHTimework, {
                id: i.refEnd?.id,
              });
            }
            // trans.delete(WHTimer, {
            //   refStart: {
            //     id: In(WHTimeWork.map((t) => t.id)),
            //   },
            //   hour: hourStart,
            //   minute: minuteStart,
            // });
            // trans.delete(WHTimer, {
            //   refEnd: {
            //     id: In(WHTimeWork.map((t) => t.id)),
            //   },
            //   hour: hourEnd,
            //   minute: minuteEnd,
            // });
          }
        }

        // virtual startDate = clearedDate || startDate< cleaeredDate, many virtual
        const workingHour = await this.repo.find({
          where: {
            date: MoreThanOrEqual(clearedDate),
            dayOfWeek,
            employee: { id: employeeId },
          },
          relations: [
            'shiftTimes',
            'employee',
            'branch',
            'shiftTimes.startTime',
            'shiftTimes.endTime',
          ],
        });
        for (const item of workingHour) {
          const workingHourNeedDel = item.shiftTimes.filter(
            (obj) =>
              moment(
                [obj.startTime.hour, obj.startTime.minute].join(':'),
                'HH:mm',
              ).isSame(moment(startTime, 'HH:mm')) &&
              moment(
                [obj.endTime.hour, obj.endTime.minute].join(':'),
                'HH:mm',
              ).isSame(moment(endTime, 'HH:mm')),
          );
          await this.repo.manager.transaction(async (trans) => {
            trans.delete(WHTimer, {
              refStart: {
                id: In(workingHourNeedDel.map((t) => t.id)),
              },
            });
            trans.delete(WHTimework, {
              id: In(workingHourNeedDel.map((t) => t.id)),
            });
          });
        }

        // await this.repo.manager.transaction(async (trans) => {
        //   trans.delete(WHTimer, {
        //     refStart: {
        //       id: In(workingHourNeedDel.map((t) => t.id)),
        //     },
        //   });
        //   trans.delete(WHTimework, {
        //     id: In(workingHourNeedDel.map((t) => t.id)),
        //   });
      });
      // create new virtual

      // get all working hour of dateOfWeek with date > request date

      // update all real working-hour
    } else {
      if (existDate) {
        const workingHourNeedDel = existDate.shiftTimes.filter(
          (obj) =>
            moment(
              [obj.startTime.hour, obj.startTime.minute].join(':'),
              'HH:mm',
            ).isSame(moment(startTime, 'HH:mm')) &&
            moment(
              [obj.endTime.hour, obj.endTime.minute].join(':'),
              'HH:mm',
            ).isSame(moment(endTime, 'HH:mm')),
        );
        await this.repo.manager.transaction(async (trans) => {
          trans.delete(WHTimer, {
            refStart: {
              id: In(workingHourNeedDel.map((t) => t.id)),
            },
          });
          trans.delete(WHTimework, {
            id: In(workingHourNeedDel.map((t) => t.id)),
          });
        });
      } else {
        await this.repo.save({
          date: clearedDate,
          dayOfWeek,
          employee,
          branch,
          shiftTimes: newShiftTimes,
        });
      }
    }

    return true;
  }

  async getList({
    startDate,
    endDate,
    branchIds,
    employeeId,
    keySearch,
  }: {
    startDate: string;
    endDate: string;
    branchIds?: UUID[];
    employeeId?: UUID;
    keySearch?: string;
  }): Promise<any[]> {
    const clearedStartDate = clearDateTime(startDate);
    const clearedEndDate = clearDateTime(endDate);

    // generate dates from clearedStartDate -> clearedEndDate
    const rangeDates = Array.from({
      length: moment(clearedEndDate).diff(clearedStartDate, 'days') + 1,
    }).map((_, i) => moment(clearedStartDate).add(i, 'days').utc().toDate());

    const byBranch =
      branchIds && branchIds.length > 0
        ? { branch: { id: In(branchIds) } }
        : {};
    const byKeySearch = keySearch
      ? { displayName: ILike(`%${keySearch}%`) }
      : {};
    const byEmployeeId = employeeId ? { id: Equal(employeeId) } : {};
    const employees = await this.employeeRepo.find({
      where: { ...byBranch, ...byKeySearch, ...byEmployeeId },
      relations: ['branch', 'status'],
      order: {
        created: 'DESC',
      },
    });
    if (!employees.length) {
      return [];
    }

    const byEmployees = { id: In(employees.map((emp) => emp.id)) };

    const [workingHours, vtWorkingHours] = await Promise.all([
      this.find({
        where: {
          date: In(rangeDates),
          employee: byEmployees,
        },
        relations: [
          'employee',
          'branch',
          'shiftTimes',
          'shiftTimes.startTime',
          'shiftTimes.endTime',
        ],
      }),
      this.virtualWorkingHourRepo.find({
        where: rangeDates.flatMap((date) => [
          {
            startDate: LessThanOrEqual(date),
            endDate: MoreThanOrEqual(date),
            employee: byEmployees,
          },
          {
            startDate: LessThanOrEqual(date),
            endDate: IsNull(),
            employee: byEmployees,
          },
        ]),
        relations: [
          'employee',
          'branch',
          'shiftTimes',
          'shiftTimes.startTime',
          'shiftTimes.endTime',
        ],
      }),
    ]);

    const workingHoursHash: Record<string, any> = workingHours.reduce(
      (hs, wh) => {
        hs[wh.employee.id] ??= {};
        hs[wh.employee.id][wh.branch.id] ??= {};
        hs[wh.employee.id][wh.branch.id][wh.date.toISOString()] = wh.shiftTimes;
        return hs;
      },
      {},
    );

    const vtWorkingHoursHash: Record<string, any> = vtWorkingHours.reduce(
      (hs, wh) => {
        hs[wh.employee.id] ??= {};
        hs[wh.employee.id][wh.branch.id] ??= {};
        hs[wh.employee.id][wh.branch.id][wh.dayOfWeek] ??= [];
        hs[wh.employee.id][wh.branch.id][wh.dayOfWeek].push(wh);
        return hs;
      },
      {},
    );

    function getWorkingHour(employeeId: string, branchId: string, date: Date) {
      return workingHoursHash[employeeId]?.[branchId]?.[
        date.toISOString()
      ]?.map((shift: WHTimework) => ({
        startTime: timerToString(shift.startTime),
        endTime: timerToString(shift.endTime),
      }));
    }

    function getVtWorkingHour(
      employeeId: string,
      branchId: string,
      date: Date,
    ) {
      const dayOfWeek = date.getDay();
      const vts: VirtualWorkingHour[] =
        vtWorkingHoursHash[employeeId]?.[branchId]?.[dayOfWeek] || [];

      const targetTime = date.getTime();
      return vts
        .find((vt) => {
          const rangeCheck = [vt.startDate.getTime(), vt.endDate?.getTime()];
          return (
            rangeCheck[0] <= targetTime &&
            (!rangeCheck[1] || targetTime <= rangeCheck[1])
          );
        })
        ?.shiftTimes.map((shift: WHTimework) => ({
          startTime: timerToString(shift.startTime),
          endTime: timerToString(shift.endTime),
        }));
    }

    const queryDayOff = this.dayOffRepo
      .createQueryBuilder('day_off')
      .leftJoinAndSelect('day_off.employee', 'employee')
      .leftJoinAndSelect('employee.branch', 'branch')
      .where(
        '(day_off.startDate <= :endTime AND (day_off.endDate IS NULL OR day_off.endDate >= :startTime))',
        {
          startTime: moment(startDate).utc().startOf('day').toISOString(),
          endTime: moment(endDate).utc().endOf('day').toISOString(),
        },
      );

    if (branchIds.length > 0) {
      queryDayOff.andWhere(
        new Brackets((subQuery) =>
          subQuery.where('branch.id IN (:...branchIds)', {
            branchIds,
          }),
        ),
      );
    }

    const dayOffList = await queryDayOff.getMany();

    return employees
      .filter(({ status }) => status.name === 'Active')
      .map(({ branch, ...employee }) => ({
        employee,
        branch,
        dates: rangeDates.map((date) => {
          const dayOffs = dayOffList.filter(
            (off) => off.employee.id === employee.id,
          );

          let dayOff = null;
          for (let off of dayOffs) {
            const startDate = new Date(off.startDate);
            startDate.setUTCHours(0, 0, 0, 0);

            const endDate = new Date(off.endDate);
            endDate.setUTCHours(23, 59, 59, 999);

            const currentDate = new Date(date);
            currentDate.setUTCHours(0, 0, 0, 0);

            if (currentDate >= startDate && currentDate <= endDate) {
              dayOff = off;
              break;
            }
          }

          if (dayOff) {
            return {
              date,
              shiftTimes:
                getWorkingHour(employee.id, branch.id, date) ??
                getVtWorkingHour(employee.id, branch.id, date) ??
                [],
              isDayOff: true,
              dayOffStartTime: dayOff.startDate,
              dayOffEndTime: dayOff.endDate,
            };
          }

          return {
            date,
            shiftTimes:
              getWorkingHour(employee.id, branch.id, date) ??
              getVtWorkingHour(employee.id, branch.id, date) ??
              [],
          };
        }),
      }));
  }
}
