import { CrudController } from 'src/core/crud/crud';
import { BaseCrudController } from 'src/core/base/base-crud.controller';
import { BaseCrud } from '../auth/decorators/base-crud.decorator';
import { WorkingHour } from './working-hour.entity';
import { WorkingHourService } from './working-hour.service';
import { Body, Get, Param, Post, Query, Req, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ApiOkResponse, ApiQuery, ApiTags } from '@nestjs/swagger';
import { QueryDto, WorkingHourResponse } from './dto/listWorkingHour.dto';
import { CreateWorkHourDto } from './dto/createWorkingHour.dto';
import { isUUID } from 'class-validator';

@BaseCrud(
  {
    model: {
      type: WorkingHour,
    },
    routes: {
      exclude: [
        'getManyBase',
        'getOneBase',
        'createManyBase',
        'createOneBase',
        'replaceOneBase',
        'updateOneBase',
        'deleteOneBase',
        'recoverOneBase',
      ],
    },
  },
  {},
)
export class WorkingHourController extends BaseCrudController<WorkingHour> {
  constructor(public service: WorkingHourService) {
    super(service);
  }
  get base(): CrudController<WorkingHour> {
    return this;
  }

  @UseGuards(JwtAuthGuard)
  @Get('/')
  @ApiQuery({ name: 'startDate', required: true })
  @ApiQuery({ name: 'endDate', required: true })
  @ApiQuery({ name: 'branch', required: false })
  @ApiQuery({ name: 'keySearch', required: false })
  @ApiOkResponse({
    type: WorkingHourResponse,
    isArray: true,
  })
  async listWorkingHour(
    @Query() { startDate, endDate, branch, keySearch }: QueryDto,
    @Req() req: Request,
  ): Promise<WorkingHourResponse[]> {
    return this.service.getList({
      startDate,
      endDate,
      branchIds: (req.headers?.['branchid']?.split(',') || []).filter((id) =>
        isUUID(id),
      ),
      keySearch,
    });
  }

  @UseGuards(JwtAuthGuard)
  @Post('/employee/:employeeId/set-v2')
  @ApiOkResponse({
    description: 'Working hour set successfully using V2 logic',
    type: Boolean,
  })
  async setWorkingHourV2(
    @Param('employeeId') employeeId: string,
    @Body() data: CreateWorkHourDto,
  ): Promise<{ success: boolean; message: string }> {
    try {
      await this.service.setWorkingHourV2(employeeId, data);
      return {
        success: true,
        message: 'Working hour set successfully',
      };
    } catch (error) {
      return {
        success: false,
        message: error.message || 'Failed to set working hour',
      };
    }
  }
}
